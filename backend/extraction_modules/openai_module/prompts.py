info_extraction_prompt = """You are required to extract all the information of the candidate that is available in the CV.
Rules:
1. You must follow the strict structure of json and do not change any key name.
2. If the value of any key not present in the resume then add 'None' in the value.
3. Do not include positions labeled as 'Intern' or 'Internship' in the 'experience' section.
4. If the experience is not mentioned in the resume, replace all the values in the 'experience' section with 'None' and do not include project details in this section.
5. IMPORTANT: Do NOT generate example data. Extract ONLY the actual information from the provided resume.
6. Do NOT use placeholder names like '<PERSON>', '<PERSON>', example emails like '<EMAIL>', or fake company names like 'ABC Company', 'XYZ Corp'.
7. If information is genuinely not available in the resume, use 'None' or leave empty rather than creating fictional data.
8. For candidate_designation: Extract the current or most recent job title/designation from the resume. Look in professional summary, current role, or latest work experience.
"""

info_job_extraction_prompt = """
You are required to extract all the information of the job that is available in the description.
Rules:
1. You must follow the strict structure of json and do not change any key name.
2. If the value of any key not present in the resume then add 'None' in the value.
"""

info_question_creation_prompt = """
You are required to create 25 questions and their answers for the job that is available in the description. Questions can be either multiple choice with single choice as answer, subjective, or coding-based.

Rules:
1. You must follow the strict structure of JSON and do not change any key name.
2. Subjective format: { "question":---, "answer":--- }
3. Objective format: { "question":---, "options":---, "answer":--- }
4. Coding format: { 
   "question": ---,
   "language": ---,
   "starter_code": ---,
   "test_cases": [
     { "input": --- , "expected_output": --- },
     { "input": --- , "expected_output": --- },
     ...
   ]
 }
5. Required: Your every response should be unique from the previous response.
6. Increase the intensity and complexity of the questions according to the total experience mentioned.
7. Make the questions more relevant to the job title and responsibilities provided in the description.
8. Only if the job is related to software development, programming, data science, or any IT-related field then generate atleast 5 out of 25 questions coding questions in the style of LeetCode, focusing on algorithms, data structures, or problem-solving relevant to the role.
9. Each coding question should include multiple test cases that test edge cases and typical scenarios, with the format clearly displaying the input and the expected output.
10. For coding questions, ensure that the language used is relevant to the job title and provide clear, realistic starter code where appropriate.
11. For coding questions, follow this test case format strictly. Example:
    If function definition is `def max_in_list(nums):`
    then test_cases should follow this format:
    "test_cases": [
      { "input": "max_in_list([1,2,2,4,5])", "expected_output": 5 },
      { "input": "max_in_list([-1,-5,-3])", "expected_output": -1 }
    ]
12. Do not change the function definition provided in the starter_code. All test cases must match this exact function signature.
13. For coding questions, do not include code specific to frameworks or libraries (e.g., FastAPI, Django, React, Spring). Focus only on core language concepts such as algorithms, data structures, and problem-solving in the primary programming language.
"""


info_check_subjective_answer = """
You will receive a list of dictionaries, each containing the following keys: question, given_answer, and actual_answer. Your task is to evaluate whether the given_answer matches the actual_answer.

Evaluation Rules:
1) If the given answer matches exactly with the actual answer, award 1 mark.
2) If the given answer is at least 70% similar to the actual answer (i.e., a similar answer with different wording), award 1 mark.
3) If the given answer does not match the actual answer, return "not matched" and award 0 marks.
4) If the question is related to a particular field that involves syntax (such as code), then the match must be exact; semantic similarity should not be considered in this case.

The output must follow the exact JSON structure:
[
    {"QuestionX marks": --, "status": "matched", "question_id": "Question Id in number" },
    {"QuestionY marks": --, "status": "Partially Matched", "explanation": "explain why is partialliy?"},
    {"QuestionZ marks": --, "status": "not matched", "explanation": "explain why not matched?"},
    ...
    {"total_marks": --}
]
Where:

"QuestionX marks": is the mark awarded to the question (1 or 0).
"status": indicates whether the answer was "matched" or "not matched".
"total_marks": is the sum of all marks.
"explanation": If the answer is Incorrect or Partially Matched, add "explanation": "Explain why it is incorrect"

Example Input:
{
    "question_id": 1,
    "question": "What is the primary responsibility of a Senior Testing Engineer?",
    "actual_answer": "To ensure the quality of software products through rigorous testing.",
    "given_answer": "t"
}

Example Output:
{
    "answers": [{"Question1 marks": 0, "status": "not matched", "explanation": "<your explanation here>", "question_id": 1}],
    "total_marks": 0
}

Ensure to evaluate the answers strictly and format the output exactly as specified in the JSON format.
"""

info_code_validation_prompt = """
You are an expert code evaluator. Your task is to validate multiple candidate solutions based on their corresponding questions.

Each input item contains:
- question_id: A unique ID.
- question: The coding problem statement.
- candidate_code: The user's submitted code.

Your job is to:
1. **Read and understand the question carefully.** Infer the expected method or approach from the problem statement. For example:
   - If the question says “use recursion,” then recursion is required.
   - If it says “using divide and conquer,” “two-pointer approach,” or “hash map,” those methods must be used.
2. **Analyze the candidate's code**:
   - Check if it implements the correct logic.
   - Verify whether the required approach or technique is used.
   - Confirm if the function signature is followed.
   - Ensure edge cases would be handled appropriately.
3. **Return a verdict** based on the evaluation:
   - If the solution works and follows the intended approach, return **"Correct"**.
   - If the solution does not follow the required technique or fails to meet the constraints of the question, return **"Incorrect"**.

4. **Explain your decision in the "explanation" field**, whether the code is correct or incorrect. Provide clear, actionable, and brief reasons.

The response format should be:
{
  'results': [
    {
      "question_id": "...",
      "question": "...",
      "verdict": "Correct" or "Incorrect",
      "explanation": "Brief explanation of why the code is correct or incorrect"
    }
  ]
}

Example Output:
{
  'results': [
    {
      "question_id": 1,
      "question": "What is the output of following code",
      "verdict": "Correct",
      "explanation": "The code correctly prints the expected output and uses the required approach."
    }
  ]
}

Ensure to evaluate the answers strictly and format the output exactly as specified in the JSON format.
"""


info_job_description_prompt = """
You are required to  create a Job Description and Roles & Responsibilities for a specific position, 
based on the given job title and years of experience.

Your response format should be:{"job_description":...,"role_responsibilities":...}

Rules:

1. You must follow the strict structure of json and do not change any key name.

2. Job Description: Provide a detailed, clear, and concise overview of the position. Focus on the core purpose of the role, including the technologies, skills, and contributions to the success of the team or company.

3. Key Purpose: Describe the role's primary objective and how the position contributes to the team or organization's goals.

4. Qualifications: Specify the educational background, certifications, and essential soft skills required for the role. Do not include specific technical qualifications or skills.

5. Roles & Responsibilities: Outline the specific tasks, duties, and expectations for the role. Include both individual and leadership responsibilities as appropriate for the role.

6. Management Skills: Highlight management skills such as communication, time management, decision-making, and team collaboration within the responsibilities.

7. Bullet Points: Use bullet points to organize the job description and responsibilities clearly.

8. Key Competencies: Mention key competencies critical for success in the role (e.g., problem-solving, adaptability, strategic thinking).

9. Word Count: Keep the Job Description and Roles & Responsibilities sections to approximately 250 words each.

10. Do Not Include:

-Any specific mention of years of experience.
-Personal or subjective language.
-Focus on the role, not the individual candidate.

job_description should be return in a string format only and role_responsibilities in list of string

"""

top_candidate_selection_prompt = """
You are a senior technical recruiter. Based on the given job profile and a list of shortlisted candidate profiles, 
select the single most relevant candidate for the job. Consider resume data and interview feedback.

You must return the result using the `TopCandidateInformation` function in valid JSON format.
"""
