from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>Field,
    DateTimeField,
    TextField,
    SQL,
    BigIntegerField,
    ForeignKeyField,
    SmallIntegerField,
)
from app.models.base import ActiveRecord
from pydantic import BaseModel
from app.models.concern.enum import EmailTemplateType, EnumField


class EmailTemplate(ActiveRecord):
    # to prevent circular import
    from app.models.business import Business

    id = AutoField()
    template_name = CharField()
    business_id = CharField()
    email_body = TextField()
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    is_default = SmallIntegerField(constraints=[SQL("DEFAULT 0")])
    template_type = EnumField(EmailTemplateType, default=EmailTemplateType.Job_Template)

    business_id = BigIntegerField(null=True)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    business = ForeignKeyField(
        Business, null=True, backref="email_templates", lazy_load=True
    )

    def info(self):
        return {
            "id": self.id,
            "template_name": self.template_name,
            "email_body": self.email_body,
            "is_default": self.is_default,
            "updated_at": self.updated_at,
            "status": self.status,
        }

    class Meta:
        table_name = "email_templates"


class EmailTemplateCreate(BaseModel):
    template_name: str
    email_body: str
