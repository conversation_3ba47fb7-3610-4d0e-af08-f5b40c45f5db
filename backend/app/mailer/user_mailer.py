from app.models import User, EmailTemplate
from app.utils import AppTemplates
from app.config import API_URL, APP_URL
from app.mailer.base_mailer import BaseMailer
import logging
from app.models.concern.enum import EmailTemplateType
from jinja2 import Environment, Template, exceptions as jinja_exceptions


class UserMailer(BaseMailer):
    @classmethod
    def reset_password_email(cls, user: User):
        logging.info("Inside UserMailer reset_password_email")
        print("Inside reset_password_email")

        subject = "Recruitease Pro - Password Reset OTP"
        context = {
            "request": None,
            "user": user,
            "api_url": API_URL,
            "app_url": APP_URL,
        }

        # Fetch dynamic email template
        template_obj = EmailTemplate.get_or_none(
            EmailTemplate.template_type == EmailTemplateType.UserResetPassword
        )

        logging.info("Fetched template object:")
        logging.info(template_obj)

        if not template_obj:
            raise Exception("Email template for User Reset Password not found.")

        # Render using Jinja2 with exception handling
        try:
            env = Environment()
            template = env.from_string(template_obj.email_body)
            rendered_body = template.render(context)
            logging.info("Template rendered successfully.")
            logging.info(f"Rendered Email Body (truncated): {rendered_body[:500]}")
        except jinja_exceptions.TemplateSyntaxError as e:
            logging.error(f"Template syntax error: {e.message} at line {e.lineno}")
            raise
        except jinja_exceptions.UndefinedError as e:
            logging.error(f"Undefined variable in template: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Unexpected error while rendering template: {str(e)}")
            raise

        # Send email
        cls.send_email(subject, user.email, rendered_body, send_to_primary=True)

    @classmethod
    def user_verify_email(cls, user: User):
        subject = "Recruitease Pro - Verify Email"
        context = {
            "request": None,
            "user": user,
            "api_url": API_URL,
            "app_url": APP_URL,
        }
        # Fetch dynamic email template
        template_obj = EmailTemplate.get_or_none(
            EmailTemplate.template_type == EmailTemplateType.UserVerifyEmail
        )

        logging.info("Fetched template object:")
        logging.info(template_obj)

        if not template_obj:
            raise Exception("Email template for User Reset Password not found.")

        # Render using Jinja2 with exception handling
        try:
            env = Environment()
            template = env.from_string(template_obj.email_body)
            rendered_body = template.render(context)
            logging.info("Template rendered successfully.")
            logging.info(f"Rendered Email Body (truncated): {rendered_body[:500]}")
        except jinja_exceptions.TemplateSyntaxError as e:
            logging.error(f"Template syntax error: {e.message} at line {e.lineno}")
            raise
        except jinja_exceptions.UndefinedError as e:
            logging.error(f"Undefined variable in template: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Unexpected error while rendering template: {str(e)}")
            raise

        cls.send_email(subject, user.email, rendered_body, send_to_primary=True)

    @classmethod
    def resend_otp_email(cls, user: User, resend_text: str):
        subject = "Recruitease Pro - New OTP"
        context = {
            "request": None,
            "user": user,
            "api_url": API_URL,
            "app_url": APP_URL,
            "resend_text": resend_text,
        }

        # Fetch dynamic email template
        template_obj = EmailTemplate.get_or_none(
            EmailTemplate.template_type == EmailTemplateType.UserResendOTP
        )

        logging.info("Fetched template object:")
        logging.info(template_obj)

        if not template_obj:
            raise Exception("Email template for User Reset Password not found.")

        # Render using Jinja2 with exception handling
        try:
            env = Environment()
            template = env.from_string(template_obj.email_body)
            rendered_body = template.render(context)
            logging.info("Template rendered successfully.")
            logging.info(f"Rendered Email Body (truncated): {rendered_body[:500]}")
        except jinja_exceptions.TemplateSyntaxError as e:
            logging.error(f"Template syntax error: {e.message} at line {e.lineno}")
            raise
        except jinja_exceptions.UndefinedError as e:
            logging.error(f"Undefined variable in template: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Unexpected error while rendering template: {str(e)}")
            raise

        cls.send_email(subject, user.email, rendered_body, send_to_primary=True)
