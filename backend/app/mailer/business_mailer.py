from app.models import Business, EmailTemplate
from app.utils import AppTemplates
from app.config import API_URL, APP_URL
from app.mailer.base_mailer import BaseMailer
from app.models.concern.enum import EmailTemplateType
from jinja2 import Template
import logging


class BusinessMailer(BaseMailer):
    @classmethod
    def business_verify_email(cls, business: Business):
        logging.info("Inside business_verify_email")
        subject = "Recruitease Pro - Verify Business Email"
        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "business": business,
            "user": business.user,
        }
        # Fetch the email template from the database
        template_obj = EmailTemplate.get_or_none(
            (
                EmailTemplate.template_type
                == EmailTemplateType.BusinessEmailVerificationOTP
            )
            & (EmailTemplate.status == 1)
        )
        if not template_obj:
            raise Exception("Email template for Offer Letter not found.")
        # Render email using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)
        cls.send_email(subject, business.email, rendered_body, send_to_primary=True)

    @classmethod
    def resend_business_otp_email(cls, business: Business, resend_text: str):
        logging.info("Inside resend_business_otp_email")
        subject = "Recruitease Pro - New OTP"
        context = {
            "request": None,
            "business": business,
            "api_url": API_URL,
            "app_url": APP_URL,
            "resend_text": resend_text,
        }
        # Fetch the email template from the database
        template_obj = EmailTemplate.get_or_none(
            (
                EmailTemplate.template_type
                == EmailTemplateType.ResendOTPNotificationBusiness
            )
            & (EmailTemplate.status == 1)
        )
        if not template_obj:
            raise Exception("Email template for Offer Letter not found.")
        # Render email using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)
        cls.send_email(subject, business.email, rendered_body, send_to_primary=True)
