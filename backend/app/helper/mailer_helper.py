import logging
import pytz
from datetime import datetime


class MailerHelper:
    @staticmethod
    async def to_timezone(value, timezone_str):
        try:
            if not value or not timezone_str:
                return ""
            if not isinstance(value, datetime):
                logging.warning("Provided value is not a datetime object")
                return str(value)
            if value.tzinfo is None:
                logging.info("Naive datetime received; assuming UTC")
                value = pytz.utc.localize(value)
            tz = pytz.timezone(timezone_str)
            return value.astimezone(tz).strftime("%d-%b-%Y %I:%M %p")
        except Exception as e:
            logging.error(f"Error in to_timezone filter: {e}")
            return str(value)
