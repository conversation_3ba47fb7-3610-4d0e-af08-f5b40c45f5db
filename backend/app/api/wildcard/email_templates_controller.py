from fastapi import APIRouter, Depends, HTTPException, status, Body, Query
from app.schema import SuccessResponse, PaginationResponse
from app.models import Business, EmailTemplate
from app.validations import StringValidate
from app.exceptions import RecordNotFoundException
from app.helper import Wildcard<PERSON><PERSON><PERSON><PERSON>per
from peewee import fn
from typing import Optional
import logging
from datetime import datetime
from pytz import UTC

router = APIRouter(
    prefix="/email_templates",
    tags=["Wildcard Email Template API"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)


# Helper function to get email_template by ID
async def get_email_template(
    id: int, business: Business = Depends(WildcardAuthHelper.validate_subdomain)
) -> EmailTemplate:
    """
    Retrieve a email_template by its ID.

    Args:
        id (int): The ID of the email_template to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        EmailTemplate: The email_template object if found.

    Raises:
        RecordNotFoundException: If the email_template does not exist.
    """
    email_template: Optional[EmailTemplate] = EmailTemplate.get_or_none(id=id)
    if not email_template:
        raise RecordNotFoundException(message="EmailTemplate does not exist")
    if email_template.is_default == 0 and email_template.business_id != business.id:
        raise RecordNotFoundException(message="EmailTemplate does not exist")
    return email_template


# ------------------------------ router functions ------------------------------


@router.post("", summary="Create specific template", response_model=SuccessResponse)
def create_template(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(...),
):
    """
    Creates a new Email template.
    Args:
        template_data (EmailTemplateCreate): Email template data to be added.
    Returns:
        SuccessResponse: A JSON response containing the message and info of the created email template.
    Raises:
        HTTPException: If the Opportunity record is not found or any other error occurs during processing.
    """

    try:
        template_name = StringValidate(
            body.get("template_name"),
            field="Name",
            required=True,
            max_length=255,
            strip=True,
        )
        email_body = StringValidate(
            body.get("email_body"), field="Email Content", required=True, strip=True
        )

        # Check if email_template already exists with the provided name and raise a Value Error
        EmailTemplate.check_record_exist_with_lower(
            EmailTemplate.template_name, template_name, business_id=business.id
        )

        email_template = EmailTemplate.create(
            template_name=template_name, email_body=email_body, business_id=business.id
        )

        return SuccessResponse(
            message="Template created successfully.", data=email_template.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get("/{id}", summary="Get specific template", response_model=SuccessResponse)
def get_specific_email_template(
    email_template: EmailTemplate = Depends(get_email_template),
):
    """
    Retrieve and render a specific email template for a given Opportunity.
    Args:
        template_name: Template name to fetch template from EmailTemplate model
    Returns:
        SuccessResponse: A JSON response containing the subject and body of the rendered email template.
    Raises:
        HTTPException: If the Opportunity record is not found or any other error occurs during processing.
    """
    try:
        return SuccessResponse(
            message="Template fetched successfully.", data=email_template.info()
        )

    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/options/all",
    summary="Get Categories as Options",
    description="Retrieve a options list of email_templates.",
    response_model=SuccessResponse,
)
def get_email_templates_options(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search term to filter email_templates"
    ),
    type_id: Optional[int] = Query(0, description="Type id of email templates"),
    showId: Optional[int] = Query(None, description="ID to prioritize in the ordering"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a options list of email_templates..

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of email_templates per page. Defaults to 10.
        search (Optional[str]): Search term to filter email_templates.
        showId (Optional[int]): ID to prioritize in the ordering.
    Returns:
    """
    try:
        base_query = EmailTemplate.select().where(
            (EmailTemplate.business_id == business.id) | (EmailTemplate.is_default == 1)
        )

        if type_id is not None:
            base_query = base_query.where(EmailTemplate.template_type == type_id)

        if search:
            search = search.strip().lower()
            base_query = base_query.where(
                fn.LOWER(EmailTemplate.template_name).contains(search)
            )

        if showId is not None:
            base_query = base_query.orwhere(EmailTemplate.id == showId)
            base_query = base_query.order_by(
                fn.IF(EmailTemplate.id == showId, 0, 1), EmailTemplate.id.desc()
            )
        else:
            base_query = base_query.order_by(EmailTemplate.id.desc())

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)
        rows = [
            {"label": record.template_name, "value": record.id} for record in records
        ]

        return SuccessResponse(data=rows, message="Options fetched successfully")
    except Exception as e:
        logging.error(f"Exception in email_template list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")


@router.get("", summary="Get all email templates", response_model=PaginationResponse)
def get_all_email_templates(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Number of items per page"),
    template_type: Optional[str] = Query(
        None, description="Filter by template type: 'email' or 'offer'"
    ),
    search: Optional[str] = Query(None, description="Search in template name"),
    status: Optional[int] = Query(
        None, ge=0, le=1, description="Filter by status: 0 (inactive) or 1 (active)"
    ),
):
    """
    Retrieve and return all email templates with pagination and filtering.

    Args:
        business: Business instance from subdomain validation
        page: Page number for pagination (default: 1)
        limit: Number of items per page (default: 10, max: 100)
        template_type: Filter by template type - 'email' or 'offer'
        search: Search term to filter templates by name
        status: Filter by template status (0 for inactive, 1 for active)

    Returns:
        PaginationResponse: A JSON response containing paginated list of email templates.
    Raises:
        HTTPException: If any error occurs during processing.
    """
    try:
        # Base query - include business templates and default templates
        query = EmailTemplate.select().where(
            (EmailTemplate.business_id == business.id) | (EmailTemplate.is_default == 1)
        )

        # Apply template type filter
        if template_type:
            if template_type.lower() == "email":
                # Email templates: exclude offer letter templates
                query = query.where(
                    ~(EmailTemplate.template_name.contains("Candidate Offer Letter"))
                    & ~(EmailTemplate.template_name.contains("offer letter"))
                )
            elif template_type.lower() == "offer":
                # Offer letter templates: include only offer letter templates
                query = query.where(
                    (EmailTemplate.template_name.contains("Candidate Offer Letter"))
                    | (EmailTemplate.template_name.contains("offer letter"))
                )

        # Apply search filter
        if search:
            query = query.where(
                EmailTemplate.template_name.contains(search)
                | EmailTemplate.email_body.contains(search)
            )

        # Apply status filter
        if status is not None:
            query = query.where(EmailTemplate.status == status)

        # Get total count for pagination
        total_count = query.count()

        # Apply pagination and ordering
        templates = query.order_by(EmailTemplate.created_at.desc()).paginate(
            page, limit
        )
        templates_data = [template.info() for template in templates]

        return PaginationResponse(
            message="Templates fetched successfully.",
            data={
                "page": page,
                "limit": limit,
                "count": total_count,
                "rows": templates_data,
            },
        )
    except Exception as e:
        logging.exception("Error fetching email templates")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/{id}/update-body",
    summary="Update email body of a template",
    response_model=SuccessResponse,
)
def update_email_body(
    body: dict = Body(...),
    email_template: EmailTemplate = Depends(get_email_template),
):
    """
    Update only the email_body field of a specific email template.

    Args:
        body: Dictionary containing the new email_body.
        email_template: Fetched via dependency injection.

    Returns:
        SuccessResponse with updated template info.
    """
    try:
        email_body = StringValidate(
            body.get("email_body"), field="Email Body", required=True, strip=True
        )

        email_template.email_body = email_body
        email_template.updated_at = datetime.now(UTC)
        email_template.save()

        return SuccessResponse(
            message="Email body updated successfully.", data=email_template.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
